import React, { useState, useEffect } from 'react';
import { usePrivy, useWallets } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import EmptyState from './components/EmptyState';
import CreateWalletModal from '../components/CreateWalletModal';
import RecoveryKeyModal from '../components/RecoveryKeyModal';
import { IoKey, IoWalletOutline, IoCheckmarkCircle } from 'react-icons/io5';
import { getDefaultWallets, setDefaultWallets, DefaultWallets } from '../utils/walletUtils';
import { getTokenBalance, BalanceRequest, BalanceResponse } from '../api/solana_api';
import QRCode from 'react-qr-code';
import { 
  Connection, 
  PublicKey, 
  SystemProgram, 
  Transaction, 
  LAMPORTS_PER_SOL,
  ComputeBudgetProgram
} from '@solana/web3.js';
import { getAssociatedTokenAddress, createAssociatedTokenAccountInstruction, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { useSessionSigners } from '../utils/sessionSigners';
import bs58 from 'bs58';

type WalletView = 'list' | 'manage';

interface WalletData {
  id: string;
  address: string;
  name: string;
  balance: string;
  isActive: boolean;
  walletClientType?: string;
  isEmbedded: boolean;
  isDefault?: boolean;
  isLoadingBalance?: boolean;
}

const Wallet = () => {
  const { ready, authenticated, user } = usePrivy();
  const { wallets } = useWallets();
  const { wallets: privySolanaWallets } = useSolanaWallets();
  const { delegateSolanaWallet, isImportedSolanaWallet } = useSessionSigners();
  const [view, setView] = useState<WalletView>('list');
  const [selectedWallet, setSelectedWallet] = useState<WalletData | null>(null);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [solanaWallets, setSolanaWallets] = useState<WalletData[]>([]);
  const [defaultWallets, setDefaultWalletsState] = useState<DefaultWallets>({});
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  
  // Withdraw form states
  const [withdrawAddress, setWithdrawAddress] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawLoading, setWithdrawLoading] = useState(false);
  const [withdrawMessage, setWithdrawMessage] = useState('');
  const [withdrawError, setWithdrawError] = useState(false);
  
  // Modal states
  const [isCreateWalletModalOpen, setIsCreateWalletModalOpen] = useState(false);
  const [isRecoveryKeyModalOpen, setIsRecoveryKeyModalOpen] = useState(false);

  const isAuthenticated = ready && authenticated;

  // Function to refresh wallet list
  const refreshWalletList = () => {
    // Trigger wallet list refresh by updating a dependency
    // The useEffect will automatically re-run when privySolanaWallets changes
    console.log('Refreshing wallet list...');
  };

  // Multiple RPC endpoints for better reliability and failover
  const rpcEndpoints = [
    import.meta.env.VITE_SOLANA_RPC_URL,
    'https://rpc.ankr.com/solana',
    'https://solana-api.projectserum.com',
    'https://solana-mainnet.g.alchemy.com/v2/demo',
    'https://api.mainnet-beta.solana.com' // Moved to last as fallback due to rate limiting
  ].filter(Boolean); // Remove any undefined values
  
  const [currentRpcIndex, setCurrentRpcIndex] = useState(0);
  const getCurrentRpcEndpoint = () => rpcEndpoints[currentRpcIndex] || rpcEndpoints[0];

  // Helper function to validate Solana address
  const isValidSolanaAddress = (address: string): boolean => {
    if (!address || typeof address !== 'string' || address.length < 32 || address.length > 44) {
      return false;
    }
    try {
      const decoded = bs58.decode(address);
      return decoded.length === 32;
    } catch {
      return false;
    }
  };

  // Load default wallets from localStorage
  useEffect(() => {
    const defaults = getDefaultWallets();
    setDefaultWalletsState(defaults);
  }, []);

  // Comprehensive Solana wallet collection logic (similar to ManageWalletsModal)
  useEffect(() => {
    if (!isAuthenticated) return;

    const walletList: WalletData[] = [];
    const seenAddresses = new Set<string>();

    // Add wallets from linkedAccounts (Solana only)
    if (user?.linkedAccounts) {
      user.linkedAccounts.forEach((account: any) => {
        if (!account || 
            !['wallet', 'smart_wallet', 'eoa_wallet'].includes(account.type) || 
            !account.address || 
            typeof account.address !== 'string' || 
            account.address.includes('@') ||
            account.address.startsWith('0x') || // Skip Ethereum wallets
            seenAddresses.has(account.address.toLowerCase())) {
          return;
        }

        const isEmbedded = account.connectorType === 'embedded' || account.walletClientType === 'privy';
        
        walletList.push({
          id: `linked-${account.address}`,
          address: account.address,
          name: account.walletClientType === 'privy' ? 'Privy Wallet' : 
                account.walletClientType === 'phantom' ? 'Phantom Wallet' :
                account.walletClientType || 'Solana Wallet',
          balance: '0', // Will be fetched separately
          isActive: false,
          walletClientType: account.walletClientType || 'unknown',
          isEmbedded,
          isDefault: false,
          isLoadingBalance: true
        });
        seenAddresses.add(account.address.toLowerCase());
      });
    }

    // Add wallets from useWallets hook (Solana only)
    if (wallets) {
      wallets.forEach((wallet: any) => {
        if (!wallet || !wallet.address || 
            wallet.address.startsWith('0x') || // Skip Ethereum wallets
            seenAddresses.has(wallet.address.toLowerCase())) {
          return;
        }

        const isEmbedded = (wallet as any).embedded || wallet.walletClientType === 'embedded' || (wallet as any).connectorType === 'embedded';
        
        walletList.push({
          id: `hook-${wallet.address}`,
          address: wallet.address,
          name: wallet.walletClientType === 'privy' ? 'Privy Wallet' : 
                wallet.walletClientType === 'phantom' ? 'Phantom Wallet' :
                wallet.walletClientType || 'Solana Wallet',
          balance: '0',
          isActive: false,
          walletClientType: wallet.walletClientType || 'unknown',
          isEmbedded,
          isDefault: false,
          isLoadingBalance: true
        });
        seenAddresses.add(wallet.address.toLowerCase());
      });
    }

    // Add Solana wallets from useSolanaWallets hook
    if (privySolanaWallets) {
      privySolanaWallets.forEach((wallet: any) => {
        if (!wallet || !wallet.address || seenAddresses.has(wallet.address.toLowerCase())) {
          return;
        }

        const isEmbedded = wallet.walletClientType === 'privy' || wallet.embedded === true;
        
        walletList.push({
          id: `solana-hook-${wallet.address}`,
          address: wallet.address,
          name: wallet.walletClientType === 'privy' ? 'Privy Wallet' : 
                wallet.walletClientType === 'phantom' ? 'Phantom Wallet' :
                wallet.walletClientType || 'Solana Wallet',
          balance: '0',
          isActive: false,
          walletClientType: wallet.walletClientType || 'unknown',
          isEmbedded,
          isDefault: false,
          isLoadingBalance: true
        });
        seenAddresses.add(wallet.address.toLowerCase());
      });
    }

    // Update default status based on localStorage
    const currentDefaults = getDefaultWallets();
    walletList.forEach(wallet => {
      wallet.isDefault = wallet.address === currentDefaults.solana;
      wallet.isActive = wallet.address === currentDefaults.solana;
    });

    // Auto-select single wallet as default if none exists
    if (walletList.length === 1 && !currentDefaults.solana) {
      const newDefaults = { ...currentDefaults, solana: walletList[0].address };
      setDefaultWallets(newDefaults);
      setDefaultWalletsState(newDefaults);
      walletList[0].isDefault = true;
      walletList[0].isActive = true;
    }

    setSolanaWallets(walletList);
  }, [isAuthenticated, user, wallets, privySolanaWallets]);

  // Fetch balances for all wallets
  useEffect(() => {
    const fetchBalances = async () => {
      if (solanaWallets.length === 0) return;

      // Fetch balance for each wallet
      const balancePromises = solanaWallets.map(async (wallet) => {
        try {
          const balanceRequest: BalanceRequest = {
            walletAddress: wallet.address
            // Not including tokenAddress will fetch SOL balance only
          };

          const response = await getTokenBalance(balanceRequest);
          
          if (response.success) {
            return {
              address: wallet.address,
              balance: response.data.solBalance || '0'
            };
          } else {
            console.error(`Failed to fetch balance for ${wallet.address}:`, response.error);
            return {
              address: wallet.address,
              balance: '0'
            };
          }
        } catch (error) {
          console.error(`Error fetching balance for ${wallet.address}:`, error);
          return {
            address: wallet.address,
            balance: '0'
          };
        }
      });

      // Wait for all balance requests to complete
      const balanceResults = await Promise.all(balancePromises);

      // Update wallet balances
      setSolanaWallets(prevWallets => 
        prevWallets.map(wallet => {
          const balanceResult = balanceResults.find(result => result.address === wallet.address);
          return {
            ...wallet,
            balance: balanceResult?.balance || '0',
            isLoadingBalance: false
          };
        })
      );
    };

    fetchBalances();
  }, [solanaWallets.length]); // Only re-run when number of wallets changes

  // Handle setting default wallet
  const handleSetDefault = (address: string) => {
    const newDefaults = { ...defaultWallets, solana: address };
    setDefaultWallets(newDefaults);
    setDefaultWalletsState(newDefaults);
    
    // Update local state
    setSolanaWallets(prevWallets => 
      prevWallets.map(wallet => ({
        ...wallet,
        isDefault: wallet.address === address,
        isActive: wallet.address === address
      }))
    );
    
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
  };

  // Handle withdraw
  const handleWithdraw = async () => {
    if (!selectedWallet) return;

    // Reset messages
    setWithdrawMessage('');
    setWithdrawError(false);

    // Validate inputs
    if (!withdrawAddress || !withdrawAmount) {
      setWithdrawMessage('Please fill in all fields');
      setWithdrawError(true);
      return;
    }

    // Validate address
    if (!isValidSolanaAddress(withdrawAddress)) {
      setWithdrawMessage('Invalid Solana address format');
      setWithdrawError(true);
      return;
    }

    // Validate amount
    const amount = parseFloat(withdrawAmount);
    if (isNaN(amount) || amount <= 0) {
      setWithdrawMessage('Please enter a valid amount');
      setWithdrawError(true);
      return;
    }

    // Check balance
    const balance = parseFloat(selectedWallet.balance);
    if (amount > balance) {
      setWithdrawMessage(`Insufficient balance. Available: ${balance.toFixed(4)} SOL`);
      setWithdrawError(true);
      return;
    }

    // Check for minimum amount (to cover transaction fees)
    const minAmount = 0.000005; // Minimum SOL for transaction fees
    if (amount < minAmount) {
      setWithdrawMessage(`Minimum amount is ${minAmount} SOL`);
      setWithdrawError(true);
      return;
    }

    setWithdrawLoading(true);
    setWithdrawMessage('Preparing transaction...');

    let retryCount = 0;
    const maxRetries = 3;

    const attemptTransaction = async (rpcIndex: number = currentRpcIndex): Promise<void> => {
      try {
        // Create connection with optimized settings and specified RPC endpoint
        const rpcEndpoint = rpcEndpoints[rpcIndex] || rpcEndpoints[0];
        const connection = new Connection(rpcEndpoint, {
          commitment: 'confirmed',
          confirmTransactionInitialTimeout: 60000
        });
        
        console.log(`Using RPC endpoint: ${rpcEndpoint}`);

        // Create public keys
        const fromPubkey = new PublicKey(selectedWallet.address);
        const toPubkey = new PublicKey(withdrawAddress);

        // Convert SOL to lamports
        const lamports = Math.floor(amount * LAMPORTS_PER_SOL);
        
        console.log('Transaction details:');
        console.log('From wallet address:', selectedWallet.address);
        console.log('From public key:', fromPubkey.toString());
        console.log('To address:', withdrawAddress);
        console.log('Amount (lamports):', lamports);

        // Get fresh blockhash with aggressive refresh strategy
        setWithdrawMessage('Getting recent blockhash...');
        let blockhashInfo;
        try {
          // Always use 'confirmed' for faster blockhash that's still reliable
          blockhashInfo = await connection.getLatestBlockhash('confirmed');
          console.log('Got blockhash:', blockhashInfo.blockhash, 'Last valid block height:', blockhashInfo.lastValidBlockHeight);
        } catch (error) {
          console.error('Failed to get blockhash from RPC:', rpcEndpoint, error);
          
          // Try next RPC endpoint if available
          if (rpcIndex < rpcEndpoints.length - 1) {
            console.log('Switching to next RPC endpoint...');
            const nextRpcIndex = rpcIndex + 1;
            setCurrentRpcIndex(nextRpcIndex);
            throw new Error('RPC endpoint failed, retrying with different endpoint...');
          }
          
          throw new Error('All RPC endpoints failed. Please try again later.');
        }

        // Create transaction with priority fees for better inclusion
        const transaction = new Transaction();
        
        // Add compute budget instructions for better transaction reliability
        // Set compute unit limit to ensure sufficient compute budget
        const computeUnitLimitInstruction = ComputeBudgetProgram.setComputeUnitLimit({
          units: 300000 // Increased compute units for reliability
        });
        transaction.add(computeUnitLimitInstruction);
        
        // Add priority fee instruction (0.0001 SOL = 100,000 lamports)
        const priorityFeeInstruction = ComputeBudgetProgram.setComputeUnitPrice({
          microLamports: 100000 // 0.0001 SOL priority fee
        });
        transaction.add(priorityFeeInstruction);

        // Add the transfer instruction
        const transferInstruction = SystemProgram.transfer({
          fromPubkey,
          toPubkey,
          lamports
        });
        transaction.add(transferInstruction);

        // Set transaction properties
        transaction.recentBlockhash = blockhashInfo.blockhash;
        transaction.feePayer = fromPubkey;
        
        console.log('Transaction setup:');
        console.log('Blockhash:', blockhashInfo.blockhash);
        console.log('Fee payer set to:', transaction.feePayer.toString());
        console.log('From pubkey:', fromPubkey.toString());
        console.log('Fee payer matches from pubkey:', transaction.feePayer.equals(fromPubkey));

        setWithdrawMessage('Please approve the transaction in your wallet...');

        // Find the Solana wallet from Privy
         const solanaWallet = privySolanaWallets.find(w => w.address === selectedWallet.address);
         if (!solanaWallet) {
           throw new Error('Wallet not found in Privy wallets');
         }

         console.log('Wallet verification:');
         console.log('Selected wallet address:', selectedWallet.address);
         console.log('Found Privy wallet address:', solanaWallet.address);
         console.log('Wallet client type:', solanaWallet.walletClientType);
         console.log('Transaction fee payer:', transaction.feePayer?.toString());
         console.log('Addresses match:', solanaWallet.address === selectedWallet.address);

         // Check if this wallet has session signing capabilities
         const hasSessionSigning = solanaWallet.walletClientType === 'privy';
         console.log('Wallet has session signing:', hasSessionSigning);
         
         if (!hasSessionSigning) {
           console.warn('⚠️ Wallet does not have session signing enabled, this may cause signature verification issues');
         }

         // For embedded wallets, ensure delegation is properly set up
         if (selectedWallet.isEmbedded && hasSessionSigning) {
           try {
             // Check if wallet is properly delegated by attempting to get the wallet instance
             console.log('Verifying embedded wallet delegation...');
             if (!solanaWallet.address || solanaWallet.address !== selectedWallet.address) {
               throw new Error('Wallet delegation verification failed');
             }
             console.log('✅ Embedded wallet delegation verified');
           } catch (delegationError) {
             console.error('❌ Wallet delegation check failed:', delegationError);
             throw new Error('Wallet is not properly delegated for signing. Please reconnect your wallet and try again.');
           }
         }

         // Check if wallet has sendTransaction method
         if (typeof solanaWallet.sendTransaction !== 'function') {
           throw new Error('Wallet does not support transaction signing');
         }

         // Get current block height to validate blockhash freshness
         const currentBlockHeight = await connection.getBlockHeight('confirmed');
         const blockhashAge = currentBlockHeight - blockhashInfo.lastValidBlockHeight;
         
         // If blockhash is getting stale (within 10 blocks of expiry), refresh it
         if (blockhashAge > -10) {
           console.log('Blockhash is getting stale, refreshing...');
           setWithdrawMessage('Refreshing blockhash for better reliability...');
           
           try {
             const freshBlockhash = await connection.getLatestBlockhash('confirmed');
             transaction.recentBlockhash = freshBlockhash.blockhash;
             blockhashInfo = freshBlockhash;
             console.log('Refreshed blockhash:', freshBlockhash.blockhash);
           } catch (error) {
             console.warn('Failed to refresh blockhash, proceeding with original:', error);
           }
           
           setWithdrawMessage('Please approve the transaction in your wallet...');
         }

         console.log('Pre-send transaction state:');
         console.log('Transaction signatures length:', transaction.signatures.length);
         console.log('Transaction fee payer:', transaction.feePayer?.toString());
         console.log('Wallet address for signing:', solanaWallet.address);
         console.log('Transaction instructions count:', transaction.instructions.length);

         // Clear any existing signatures to prevent conflicts
         transaction.signatures = [];
         console.log('Cleared transaction signatures for clean signing');

         // Ensure the fee payer matches the wallet address
         if (transaction.feePayer?.toString() !== solanaWallet.address) {
           console.warn('Fee payer mismatch detected, correcting...');
           transaction.feePayer = new PublicKey(solanaWallet.address);
         }

         // Validate transaction before sending
         console.log('Final transaction validation:');
         console.log('Fee payer:', transaction.feePayer?.toString());
         console.log('Wallet address:', solanaWallet.address);
         console.log('Blockhash set:', !!transaction.recentBlockhash);
         console.log('Instructions count:', transaction.instructions.length);
         console.log('Signatures cleared:', transaction.signatures.length === 0);

         // Alternative approach: Use signTransaction instead of sendTransaction to avoid internal conflicts
         let signature;
         try {
           // First try the standard sendTransaction method
           signature = await solanaWallet.sendTransaction(transaction, connection);
         } catch (signError: any) {
            console.log('sendTransaction failed, trying alternative signing approach:', signError?.message || signError);
           
           // Check for signature verification errors and provide specific guidance
           if (signError?.message?.includes('Signature verification failed') || 
               signError?.message?.includes('Invalid signature')) {
             console.error('🔍 Signature verification error details:', {
               message: signError.message,
               walletAddress: selectedWallet.address,
               walletType: solanaWallet.walletClientType,
               hasSessionSigning: hasSessionSigning,
               stack: signError.stack
             });
             
             // For wallets without proper session signing, provide specific guidance
             if (!hasSessionSigning) {
               throw new Error('Wallet signing failed: This wallet does not have session signing enabled. Please reconnect your wallet or try importing it again to enable session signing.');
             }
             
             // Check if wallet is imported before attempting delegation
             if (isImportedSolanaWallet(selectedWallet.address)) {
               console.log('⚠️ Wallet is imported and cannot be delegated, proceeding directly to manual signing...');
               // Skip delegation for imported wallets and proceed to manual signing
             } else {
               // Attempt to re-delegate the wallet before failing
               console.log('🔄 Attempting to re-delegate wallet due to signature verification failure...');
               try {
                 const delegationResult = await delegateSolanaWallet(selectedWallet.address);
               
               if (delegationResult === true) {
                 console.log('✅ Wallet re-delegation successful, waiting for state synchronization...');
                 
                 // Trigger session signer re-initialization across the app
                 window.dispatchEvent(new CustomEvent('initialize-session-signers', {
                   detail: { source: 'wallet-withdrawal', address: selectedWallet.address }
                 }));
                 
                 // Wait for delegation state to propagate
                 await new Promise(resolve => setTimeout(resolve, 2000));
                 console.log('🔄 Retrying transaction after delegation state sync...');
                 
                 // Clear signatures and ensure clean transaction state before retry
                 transaction.signatures = [];
                 transaction.feePayer = new PublicKey(solanaWallet.address);
                 console.log('🧹 Cleared transaction state for clean retry');
                 
                 // Retry the transaction once after successful re-delegation
                 try {
                   signature = await solanaWallet.sendTransaction(transaction, connection);
                   console.log('✅ Transaction successful after re-delegation:', signature);
                 } catch (retryError: any) {
                   console.error('❌ Transaction still failed after re-delegation:', retryError);
                   throw new Error('Transaction failed even after wallet re-delegation. Please contact support.');
                 }
               } else if (delegationResult === 'imported') {
                  console.log('⚠️ Wallet is imported and cannot use session signing, proceeding with manual signing...');
                  // For imported wallets, skip delegation and proceed directly to manual signing
                  // This will be handled by the alternative signing method below
                } else {
                  console.log('⚠️ Wallet delegation failed, proceeding with manual signing...');
                  // If delegation fails, we'll try the alternative signing method below
                }
                } catch (delegationError: any) {
                  console.error('❌ Wallet re-delegation failed:', delegationError);
                  console.log('⚠️ Delegation error occurred, proceeding with manual signing...');
                  // If delegation throws an error, we'll try the alternative signing method below
                }
              }
           }
           
           // Alternative: Use signTransaction if available, then send manually
            if (typeof solanaWallet.signTransaction === 'function') {
              console.log('Attempting manual signing and sending...');
              try {
                // Ensure clean transaction state for manual signing
                transaction.signatures = [];
                transaction.feePayer = new PublicKey(solanaWallet.address);
                console.log('🧹 Cleared transaction state for manual signing');
                
                const signedTransaction = await solanaWallet.signTransaction(transaction);
                console.log('Transaction signed successfully, sending to network...');
                signature = await connection.sendRawTransaction(signedTransaction.serialize(), {
                  skipPreflight: false,
                  preflightCommitment: 'confirmed'
                });
                console.log('Transaction sent via alternative method, signature:', signature);
              } catch (altError: any) {
                console.error('Alternative signing method also failed:', altError?.message || altError);
                throw signError; // Throw the original error for consistency
              }
            } else {
              console.log('No signTransaction method available, wallet type:', solanaWallet.walletClientType);
              throw signError; // Throw the original error
            }
         }
         
         // Reset RPC index to first endpoint on successful transaction
         if (rpcIndex > 0) {
           setCurrentRpcIndex(0);
           console.log('Transaction successful, reset to primary RPC endpoint');
         }
         
         setWithdrawMessage(`Transaction sent! Signature: ${signature.slice(0, 8)}...${signature.slice(-8)}`);
         setWithdrawError(false);
         
         // Start confirmation polling
         pollForConfirmation(signature, connection);

      } catch (error) {
        console.error('Transaction attempt failed:', error);
        
        // Check if this is an RPC-related error
        const isRpcError = error instanceof Error && 
          (error.message.includes('403') ||
           error.message.includes('Forbidden') ||
           error.message.includes('RPC endpoint failed') ||
           error.message.includes('failed to get recent blockhash') ||
           error.message.includes('Connection refused') ||
           error.message.includes('timeout'));

        // Check if this is a blockhash expiration error
        const isBlockhashError = error instanceof Error && 
          (error.message.includes('blockhash') || 
           error.message.includes('expired') ||
           error.message.includes('not found') ||
           error.message.includes('Transaction simulation failed') ||
           error.message.includes('Blockhash not found'));

        // Try different RPC endpoint for RPC errors
        if (isRpcError && rpcIndex < rpcEndpoints.length - 1 && retryCount < maxRetries) {
          retryCount++;
          const nextRpcIndex = rpcIndex + 1;
          setWithdrawMessage(`RPC endpoint failed, switching to backup... (${retryCount}/${maxRetries})`);
          setCurrentRpcIndex(nextRpcIndex);
          
          console.log(`Switching from RPC ${rpcIndex} (${rpcEndpoints[rpcIndex]}) to RPC ${nextRpcIndex} (${rpcEndpoints[nextRpcIndex]})`);
          
          await new Promise(resolve => setTimeout(resolve, 1000));
          return attemptTransaction(nextRpcIndex);
        }

        // Retry with fresh blockhash if needed
        if (isBlockhashError && retryCount < maxRetries) {
          retryCount++;
          setWithdrawMessage(`Transaction expired, getting fresh blockhash... (${retryCount}/${maxRetries})`);
          
          // Exponential backoff: wait longer between retries
          const waitTime = Math.min(1000 * Math.pow(2, retryCount - 1), 5000);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          
          return attemptTransaction(rpcIndex);
        }

        handleTransactionError(error);
      }
    };

    const handleTransactionError = (error: any) => {
      let errorMessage = 'Transaction failed';
      
      if (error instanceof Error) {
        if (error.message.includes('rejected') || error.message.includes('cancelled')) {
          errorMessage = 'Transaction rejected by user';
        } else if (error.message.includes('insufficient')) {
          errorMessage = 'Insufficient balance for transaction and fees';
        } else if (error.message.includes('blockhash') || 
                   error.message.includes('expired') ||
                   error.message.includes('Transaction simulation failed') ||
                   error.message.includes('Blockhash not found')) {
          errorMessage = 'Transaction expired due to network congestion. Please try again with a fresh transaction.';
        } else if (error.message.includes('network') || error.message.includes('timeout')) {
           errorMessage = 'Network error. Please check your connection and try again.';
         } else if (error.message.includes('Failed to get recent blockhash') || 
                    error.message.includes('All RPC endpoints failed')) {
           errorMessage = 'Unable to connect to Solana network. All RPC endpoints are currently unavailable. Please try again later.';
         } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
           errorMessage = 'RPC access denied. The network may be experiencing high traffic. Please try again.';
        } else if (error.message.includes('Signature verification failed') || 
                   error.message.includes('Invalid signature')) {
          errorMessage = 'Transaction signing failed. Please ensure your wallet is properly connected and try again.';
          console.error('Signature verification error details:', {
            errorMessage: error.message,
            walletAddress: selectedWallet?.address,
            stack: error.stack
          });
        } else {
          errorMessage = error.message;
        }
      }
      
      setWithdrawMessage(errorMessage);
      setWithdrawError(true);
      setWithdrawLoading(false);
    };

    const pollForConfirmation = async (signature: string, connection: Connection) => {
      setWithdrawMessage('Waiting for confirmation...');
      
      let confirmationAttempts = 0;
      const maxConfirmationAttempts = 60; // 60 seconds
      
      const checkConfirmation = async () => {
        confirmationAttempts++;
        try {
          const { value: status } = await connection.getSignatureStatus(signature, {
            searchTransactionHistory: true
          });
          
          if (status?.confirmationStatus === 'confirmed' || status?.confirmationStatus === 'finalized') {
            setWithdrawMessage(`Transaction confirmed! View on Solscan: https://solscan.io/tx/${signature}`);
            setWithdrawError(false);
            setWithdrawLoading(false);
            
            // Clear form
            setWithdrawAddress('');
            setWithdrawAmount('');
            
            // Refresh balance after 2 seconds
            setTimeout(() => {
              refreshWalletBalance();
            }, 2000);
            
            // Close modal after 5 seconds
            setTimeout(() => {
              setShowWithdrawModal(false);
              setWithdrawMessage('');
            }, 5000);
          } else if (status?.err) {
            // Transaction failed
            setWithdrawMessage(`Transaction failed: ${JSON.stringify(status.err)}`);
            setWithdrawError(true);
            setWithdrawLoading(false);
          } else if (confirmationAttempts >= maxConfirmationAttempts) {
            // Timeout
            setWithdrawMessage(`Transaction sent but not confirmed after ${maxConfirmationAttempts} seconds. Check on Solscan: https://solscan.io/tx/${signature}`);
            setWithdrawError(false);
            setWithdrawLoading(false);
          } else {
            // Continue polling
            setTimeout(checkConfirmation, 1000);
          }
        } catch (error) {
          console.error('Error checking confirmation:', error);
          if (confirmationAttempts >= 10) {
            setWithdrawMessage(`Transaction sent. Check status on Solscan: https://solscan.io/tx/${signature}`);
            setWithdrawError(false);
            setWithdrawLoading(false);
          } else {
            setTimeout(checkConfirmation, 1000);
          }
        }
      };
      
      checkConfirmation();
    };

    const refreshWalletBalance = async () => {
      try {
        const balanceRequest: BalanceRequest = {
          walletAddress: selectedWallet.address
        };
        const response = await getTokenBalance(balanceRequest);
        if (response.success) {
          setSolanaWallets(prevWallets => 
            prevWallets.map(wallet => 
              wallet.address === selectedWallet.address 
                ? { ...wallet, balance: response.data.solBalance || '0' }
                : wallet
            )
          );
          setSelectedWallet(prev => 
            prev && prev.address === selectedWallet.address
              ? { ...prev, balance: response.data.solBalance || '0' }
              : prev
          );
        }
      } catch (error) {
        console.error('Error refreshing balance:', error);
      }
    };

    // Start the transaction attempt
    await attemptTransaction();
  };

  const renderWalletList = () => (
    <div className="space-y-4">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-2">
            <IoCheckmarkCircle className="text-green-500" size={20} />
            <p className="text-green-400 text-sm">Default Solana wallet updated successfully!</p>
          </div>
        </div>
      )}

      {/* Header Actions */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-white">My Solana Wallets</h2>
        <div className="flex items-center gap-3">
          {solanaWallets.length > 0 && (
            <div className="text-sm text-gray-400">
              {solanaWallets.length} wallet{solanaWallets.length > 1 ? 's' : ''} connected
            </div>
          )}
          <button
            onClick={() => setIsCreateWalletModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-[#14FFA2]/20 text-[#14FFA2] rounded-lg hover:bg-[#14FFA2]/30 transition-all duration-200 font-medium text-sm"
          >
            <IoWalletOutline className="w-4 h-4" />
            Create Wallet
          </button>
        </div>
      </div>

      {/* Wallet List */}
      {solanaWallets.length === 0 ? (
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-12 border border-gray-800/50">
          <EmptyState
            title="No Solana Wallets Connected"
            description="Please connect a Solana wallet using Privy to continue"
            icon="wallet"
          />
        </div>
      ) : (
        <div className="grid gap-4">
          {solanaWallets.map((wallet) => (
            <div
              key={wallet.id}
              className={`relative group bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border transition-all duration-300 cursor-pointer ${
                wallet.isDefault
                  ? 'border-[#7FFFD4]/50 shadow-lg shadow-[#7FFFD4]/10'
                  : 'border-gray-800/50 hover:border-gray-700/50'
              }`}
              onClick={() => setSelectedWallet(wallet)}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-[#7FFFD4]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl" />
              <div className="relative flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">{wallet.name}</h3>
                    {wallet.isDefault && (
                      <span className="px-2 py-1 bg-[#7FFFD4] text-black text-xs rounded-full font-medium">
                        Default
                      </span>
                    )}
                    <span className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full">
                      {wallet.isEmbedded ? 'Embedded' : 'External'}
                    </span>
                    {solanaWallets.length === 1 && (
                      <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                        Auto-selected
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400 font-mono">{wallet.address}</p>
                  <p className="text-lg font-semibold text-white mt-2">
                    {wallet.isLoadingBalance ? (
                      <span className="animate-pulse">Loading...</span>
                    ) : (
                      `${parseFloat(wallet.balance).toFixed(4)} SOL`
                    )}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {!wallet.isDefault && solanaWallets.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSetDefault(wallet.address);
                      }}
                      className="px-3 py-2 bg-gray-700/50 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-all duration-200 text-sm"
                    >
                      Set Default
                    </button>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedWallet(wallet);
                      setView('manage');
                    }}
                    className="px-4 py-2 bg-gray-700/50 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-all duration-200 text-sm"
                  >
                    Manage
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );


  const renderManageWallet = () => (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <button
          onClick={() => setView('list')}
          className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Wallets
        </button>
      </div>

      {selectedWallet && (
        <div className="grid gap-6">
          {/* Wallet Info */}
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">{selectedWallet.name}</h2>
              <div className="flex items-center gap-2">
                {selectedWallet.isDefault && (
                  <span className="px-3 py-1 bg-[#7FFFD4] text-black text-sm rounded-full font-medium">
                    Default Wallet
                  </span>
                )}
                <span className="px-3 py-1 bg-gray-700/50 text-gray-300 text-sm rounded-full">
                  {selectedWallet.isEmbedded ? 'Embedded' : 'External'}
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-400 font-mono mb-4">{selectedWallet.address}</p>
            <div className="text-3xl font-bold text-white">
              {selectedWallet.isLoadingBalance ? (
                <span className="animate-pulse">Loading...</span>
              ) : (
                `${parseFloat(selectedWallet.balance).toFixed(4)} SOL`
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => setShowDepositModal(true)}
              className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 hover:border-[#7FFFD4]/50 transition-all duration-300 group"
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-[#7FFFD4]/20 rounded-lg flex items-center justify-center group-hover:bg-[#7FFFD4]/30 transition-colors">
                  <svg className="w-6 h-6 text-[#7FFFD4]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                  </svg>
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-semibold text-white">Deposit</h3>
                  <p className="text-sm text-gray-400">Add funds to your wallet</p>
                </div>
              </div>
            </button>

            <button
              onClick={() => setShowWithdrawModal(true)}
              className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 hover:border-[#FF329B]/50 transition-all duration-300 group"
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-[#FF329B]/20 rounded-lg flex items-center justify-center group-hover:bg-[#FF329B]/30 transition-colors">
                  <svg className="w-6 h-6 text-[#FF329B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 13l-5 5m0 0l-5-5m5 5V6" />
                  </svg>
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-semibold text-white">Withdraw</h3>
                  <p className="text-sm text-gray-400">Send funds from your wallet</p>
                </div>
              </div>
            </button>
          </div>

          {/* Wallet Actions */}
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
            <h3 className="text-lg font-semibold text-white mb-4">Wallet Actions</h3>
            <div className="space-y-3">
              {!selectedWallet.isDefault && solanaWallets.length > 1 && (
                <button 
                  onClick={() => handleSetDefault(selectedWallet.address)}
                  className="w-full px-4 py-3 bg-gray-700/50 text-left rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <span className="text-white">Set as Default Wallet</span>
                </button>
              )}
              <button 
                onClick={() => setIsRecoveryKeyModalOpen(true)}
                className="w-full flex items-center gap-3 px-4 py-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors"
              >
                <IoKey className="w-5 h-5 text-gray-300" />
                <span className="text-white">Export Private Key</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-full text-white">
      {view === 'list' && renderWalletList()}
      {view === 'manage' && renderManageWallet()}

      {/* Deposit Modal */}
      {showDepositModal && selectedWallet && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-[#181C20] rounded-xl p-6 max-w-md w-full border border-gray-800">
            <h3 className="text-xl font-semibold text-white mb-4">Deposit SOL</h3>
            
            {/* QR Code */}
            <div className="flex justify-center mb-6">
              <div className="bg-white p-4 rounded-lg">
                <QRCode
                  value={selectedWallet.address}
                  size={200}
                  level="H"
                />
              </div>
            </div>
            
            {/* Wallet Address */}
            <div className="bg-[#1D2226] rounded-lg p-4 mb-4">
              <p className="text-sm text-gray-400 mb-2">Wallet Address</p>
              <p className="text-sm font-mono text-white break-all">{selectedWallet.address}</p>
            </div>
            
            {/* Copy Button */}
            <button
              onClick={() => {
                navigator.clipboard.writeText(selectedWallet.address);
                // You could add a toast notification here
              }}
              className="w-full px-4 py-3 mb-4 bg-[#7FFFD4]/20 text-[#7FFFD4] rounded-lg hover:bg-[#7FFFD4]/30 transition-colors flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              Copy Address
            </button>
            
            <div className="bg-blue-900/20 border border-blue-700/50 rounded-lg p-4 mb-6">
              <p className="text-sm text-blue-200">Scan the QR code or send SOL to the address above to deposit funds.</p>
            </div>
            
            <button
              onClick={() => setShowDepositModal(false)}
              className="w-full px-4 py-3 bg-gray-700/50 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Withdraw Modal */}
      {showWithdrawModal && selectedWallet && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-[#181C20] rounded-xl p-6 max-w-md w-full border border-gray-800">
            <h3 className="text-xl font-semibold text-white mb-4">Withdraw SOL</h3>
            
            {/* Current Balance */}
            <div className="bg-[#1D2226] rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-400 mb-1">Available Balance</p>
              <p className="text-2xl font-bold text-white">{parseFloat(selectedWallet.balance).toFixed(4)} SOL</p>
            </div>
            
            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Recipient Address
                </label>
                <input
                  type="text"
                  value={withdrawAddress}
                  onChange={(e) => setWithdrawAddress(e.target.value)}
                  placeholder="Enter recipient Solana address"
                  className="w-full px-4 py-3 bg-[#1D2226] border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#7FFFD4]/50"
                  disabled={withdrawLoading}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Amount (SOL)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={withdrawAmount}
                    onChange={(e) => setWithdrawAmount(e.target.value)}
                    placeholder="0.00"
                    step="0.0001"
                    min="0"
                    className="w-full px-4 py-3 bg-[#1D2226] border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#7FFFD4]/50 pr-16"
                    disabled={withdrawLoading}
                  />
                  <button
                    onClick={() => setWithdrawAmount(selectedWallet.balance)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 px-3 py-1 bg-gray-700/50 text-gray-300 rounded text-sm hover:bg-gray-700 hover:text-white transition-colors"
                    disabled={withdrawLoading}
                  >
                    MAX
                  </button>
                </div>
              </div>
            </div>
            
            {/* Message Display */}
            {withdrawMessage && (
              <div className={`mb-4 p-4 rounded-lg ${
                withdrawError 
                  ? 'bg-red-900/20 border border-red-700/50 text-red-400'
                  : 'bg-blue-900/20 border border-blue-700/50 text-blue-200'
              }`}>
                <p className="text-sm break-all">{withdrawMessage}</p>
              </div>
            )}
            
            <div className="flex gap-3">
              <button
                onClick={handleWithdraw}
                disabled={withdrawLoading || !withdrawAddress || !withdrawAmount}
                className={`flex-1 px-4 py-3 rounded-lg transition-colors flex items-center justify-center gap-2 ${
                  withdrawLoading || !withdrawAddress || !withdrawAmount
                    ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                    : 'bg-[#7FFFD4]/20 text-[#7FFFD4] hover:bg-[#7FFFD4]/30'
                }`}
              >
                {withdrawLoading ? (
                  <>
                    <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  'Send'
                )}
              </button>
              <button
                onClick={() => {
                  setShowWithdrawModal(false);
                  setWithdrawAddress('');
                  setWithdrawAmount('');
                  setWithdrawMessage('');
                  setWithdrawError(false);
                }}
                disabled={withdrawLoading}
                className="px-6 py-3 border border-gray-700/50 text-gray-300 rounded-lg hover:border-gray-600 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Wallet Modal */}
      <CreateWalletModal
        isOpen={isCreateWalletModalOpen}
        onClose={() => setIsCreateWalletModalOpen(false)}
        onWalletCreated={(wallet) => {
          console.log('New wallet created:', wallet);
          // The wallet list will automatically refresh due to the useEffect dependency on privySolanaWallets
          refreshWalletList();
        }}
      />

      {/* Recovery Key Modal */}
      <RecoveryKeyModal
        isOpen={isRecoveryKeyModalOpen}
        onClose={() => setIsRecoveryKeyModalOpen(false)}
        selectedWallet={selectedWallet ? {
          address: selectedWallet.address,
          isEmbedded: selectedWallet.isEmbedded,
          walletClientType: selectedWallet.walletClientType,
          name: selectedWallet.name
        } : undefined}
      />
    </div>
  );
};

export default Wallet;